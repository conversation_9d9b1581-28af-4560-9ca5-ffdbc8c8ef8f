from datetime import datetime
from enum import StrEnum
from typing import Optional, List

import strawberry
from pydantic import BaseModel


@strawberry.enum
class DeviceStatusEnum(StrEnum):
    NOT_CONNECTED = "not_connected"
    CONNECTED = "connected"
    RECONNECT = "reconnect"


@strawberry.enum
class DeviceTypeEnum(StrEnum):
    WITHINGS = "withings"
    FITBIT = "fitbit"
    DEXCOM = "dexcom"
    OURARING = "ouraring"
    TRANSTEK = "transtek"


@strawberry.type
class DeviceStatus:
    status: DeviceStatusEnum
    device: DeviceTypeEnum


@strawberry.type
class Subscription:
    expires_in: int


@strawberry.type
class DetailedConnectionStatus(DeviceStatus):
    token: Optional[str] = None
    healthy: Optional[bool] = None
    account_id: Optional[str] = None
    subscription: Optional[Subscription] = None
    auth_url: Optional[str] = None


class Measure(BaseModel):
    value: float
    unit: str
    created_at: datetime


class Device(BaseModel):
    id: str
    device_type: str
    last_synced_at: datetime


class LatestData(BaseModel):
    last_ciba_sync: Optional[datetime] = None
    last_device_sync: Optional[datetime] = None
    measures: List[Measure] = []
    devices: List[Device] = []


class SyncProcessing(BaseModel):
    success: bool


class TranstekTrackingData(BaseModel):
    tracking_number: str
    carrier: str
